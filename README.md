# Auto SMS Register - 自动化接码系统

[![Python Version](https://img.shields.io/badge/python-3.9+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

一个基于Python + Playwright的完整自动化接码解决方案，支持多平台集成、智能验证码识别和浏览器自动化注册。

## ✨ 特性

- 🌐 **多接码平台支持** - 集成5sim等主流接码平台
- 🤖 **浏览器自动化** - 基于Playwright的强大自动化引擎
- 🧠 **智能验证码识别** - 支持多种格式的验证码提取
- ⚡ **异步任务调度** - 高效的并发处理能力
- 🔧 **灵活配置管理** - YAML配置文件 + 环境变量支持
- 📊 **完善监控日志** - 结构化日志和性能指标
- 🛡️ **错误恢复机制** - 智能重试和断路器模式
- 🎯 **CLI友好界面** - 简洁易用的命令行工具

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone https://github.com/auto-sms-register/auto-sms-register.git
cd auto-sms-register

# 安装依赖
pip install -e .

# 安装开发依赖
pip install -e ".[dev]"

# 安装Playwright浏览器
playwright install
```

### 基础使用

```bash
# 配置接码平台API密钥
export FIVESIM_API_KEY="your_api_key_here"

# 启动注册任务
auto-sms-register start --website example.com --count 5

# 查看任务状态
auto-sms-register status

# 查看帮助
auto-sms-register --help
```

## 📖 文档

- [安装指南](docs/installation.md)
- [配置说明](docs/configuration.md)
- [API文档](docs/api.md)
- [开发指南](docs/development.md)

## 🏗️ 架构

```
auto-sms-register/
├── src/auto_sms_register/
│   ├── core/           # 核心业务逻辑
│   ├── platforms/      # 接码平台集成
│   ├── database/       # 数据库模型
│   ├── utils/          # 工具函数
│   └── cli/            # 命令行界面
├── tests/              # 测试文件
├── docs/               # 文档
└── configs/            # 配置文件
```

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和网站服务条款。使用者需对自己的行为负责。

## 📞 支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/auto-sms-register/auto-sms-register/issues)
- 💬 讨论: [GitHub Discussions](https://github.com/auto-sms-register/auto-sms-register/discussions)
