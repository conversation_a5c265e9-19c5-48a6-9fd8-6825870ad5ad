# Auto SMS Register 配置示例文件
# 复制此文件为 config.yaml 并修改相应配置

# 接码平台配置
platforms:
  fivesim:
    api_key: "${FIVESIM_API_KEY}"  # 从环境变量读取
    base_url: "https://5sim.net/v1"
    timeout: 30
    retry_attempts: 3
    
# 浏览器配置
browser:
  headless: true
  timeout: 30000
  viewport:
    width: 1920
    height: 1080
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
# 数据库配置
database:
  url: "sqlite:///auto_sms_register.db"
  echo: false
  pool_size: 5
  max_overflow: 10
  
# 任务调度配置
scheduler:
  max_concurrent_tasks: 5
  task_timeout: 300
  retry_attempts: 3
  retry_delay: 5
  
# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "7 days"
  
# 验证码提取配置
code_extraction:
  patterns:
    - "\\b\\d{4,8}\\b"  # 4-8位数字
    - "\\b[A-Z0-9]{4,8}\\b"  # 4-8位字母数字组合
  timeout: 60
  max_attempts: 5
