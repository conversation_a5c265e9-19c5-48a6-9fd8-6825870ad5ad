"""
Auto SMS Register - 自动化接码系统

一个基于Python + Playwright的完整自动化接码解决方案，支持：
- 多接码平台集成（5sim等）
- 浏览器自动化注册
- 智能验证码识别
- 异步任务调度
- 完善的错误处理和重试机制

Author: Auto SMS Register Team
License: MIT
"""

__version__ = "0.1.0"
__author__ = "Auto SMS Register Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 导出主要的公共API (暂时注释掉，等模块实现后再启用)
# from .core.scheduler import TaskScheduler
# from .core.browser_engine import BrowserEngine
# from .core.code_extractor import CodeExtractor
# from .platforms.factory import PlatformFactory
# from .database.models import PhoneNumber, SMSMessage, Task
# from .utils.config import ConfigManager
# from .utils.exceptions import (
#     SMSRegistrationError,
#     PlatformError,
#     BrowserError,
#     CodeExtractionError,
# )

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",

    # 核心组件 (暂时注释掉，等模块实现后再启用)
    # "TaskScheduler",
    # "BrowserEngine",
    # "CodeExtractor",
    # "PlatformFactory",

    # 数据模型 (暂时注释掉，等模块实现后再启用)
    # "PhoneNumber",
    # "SMSMessage",
    # "Task",

    # 工具类 (暂时注释掉，等模块实现后再启用)
    # "ConfigManager",

    # 异常类 (暂时注释掉，等模块实现后再启用)
    # "SMSRegistrationError",
    # "PlatformError",
    # "BrowserError",
    # "CodeExtractionError",
]

# 设置日志
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())

# 类型检查支持标记文件已创建
