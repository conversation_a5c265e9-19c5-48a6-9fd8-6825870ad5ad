[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "auto-sms-register"
version = "0.1.0"
description = "自动化接码系统 - 基于Python + Playwright的完整解决方案"
authors = [
    {name = "Auto SMS Register Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["sms", "automation", "playwright", "registration", "verification"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Topic :: Communications :: Telephony",
]

dependencies = [
    "playwright>=1.40.0",
    "sqlalchemy>=2.0.0",
    "aiohttp>=3.9.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "click>=8.1.0",
    "rich>=13.7.0",
    "loguru>=0.7.0",
    "pyyaml>=6.0.1",
    "tenacity>=8.2.0",
    "cryptography>=41.0.0",
    "asyncio-mqtt>=0.16.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
    "isort>=5.12.0",
    "pre-commit>=3.5.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
]

[project.scripts]
auto-sms-register = "auto_sms_register.cli.main:cli"

[project.urls]
Homepage = "https://github.com/auto-sms-register/auto-sms-register"
Documentation = "https://auto-sms-register.readthedocs.io/"
Repository = "https://github.com/auto-sms-register/auto-sms-register.git"
"Bug Tracker" = "https://github.com/auto-sms-register/auto-sms-register/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
auto_sms_register = ["py.typed", "configs/*.yaml", "configs/*.json"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["auto_sms_register"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "playwright.*",
    "loguru.*",
    "tenacity.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
